'use client';

import React from 'react';
import { SchoolCreationForm } from '@/components/organisms/SchoolCreationForm';

export default function SchoolCreationFormTestPage() {
  return (
    <div className="min-h-screen bg-base-200 py-8">
      <div className="container mx-auto px-4">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-base-content mb-2">
            School Creation Form Test
          </h1>
          <p className="text-base-content/70 text-lg">
            Testing the SchoolCreationForm component with DaisyUI styling
          </p>
        </div>

        {/* Component Demo */}
        <div className="max-w-4xl mx-auto">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title text-primary mb-4">
                📝 School Creation Form Component
              </h2>
              
              {/* Features List */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3">✨ Features Implemented:</h3>
                <ul className="list-disc list-inside space-y-2 text-sm">
                  <li>✅ <strong>DaisyUI Components:</strong> Pure DaisyUI styling with input, button, alert classes</li>
                  <li>✅ <strong>Form Structure:</strong> Proper fieldset and legend elements</li>
                  <li>✅ <strong>Required Fields:</strong> School Name, Address, Phone, Email</li>
                  <li>✅ <strong>Client-Side Validation:</strong> Zod schema with React Hook Form</li>
                  <li>✅ <strong>Server Action Integration:</strong> Calls handleCreateSchoolAction</li>
                  <li>✅ <strong>Success/Error Handling:</strong> DaisyUI alert components</li>
                  <li>✅ <strong>Loading States:</strong> Button loading spinner during submission</li>
                  <li>✅ <strong>Accessibility:</strong> Proper labels, ARIA attributes, and form structure</li>
                </ul>
              </div>

              {/* Form Component */}
              <div className="divider">Form Component</div>
              <SchoolCreationForm />
            </div>
          </div>

          {/* Technical Details */}
          <div className="card bg-base-100 shadow-xl mt-8">
            <div className="card-body">
              <h2 className="card-title text-secondary mb-4">
                🔧 Technical Implementation
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2">Validation Schema:</h3>
                  <ul className="text-sm space-y-1">
                    <li>• Name: Required, max 255 chars</li>
                    <li>• Address: Required, max 500 chars</li>
                    <li>• Phone: Required, regex pattern</li>
                    <li>• Email: Required, valid email format</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">DaisyUI Classes Used:</h3>
                  <ul className="text-sm space-y-1">
                    <li>• <code>input input-bordered</code></li>
                    <li>• <code>btn btn-primary</code></li>
                    <li>• <code>alert alert-success/error</code></li>
                    <li>• <code>form-control</code></li>
                    <li>• <code>label label-text</code></li>
                  </ul>
                </div>
              </div>

              <div className="mt-4">
                <h3 className="font-semibold mb-2">Server Action:</h3>
                <p className="text-sm">
                  Uses <code>handleCreateSchoolAction</code> from <code>@/actions/school.action</code> 
                  which calls the backend API endpoint for school creation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
